1. **Add histogram visualization**: Create histogram bars to visualize the different tilings/hash functions used by the CMAC algorithm. The histograms should show how the input data is distributed across the various hash buckets for each tiling layer, helping users understand the CMAC's memory organization and coverage patterns.

2. **Implement lesion functionality**: Add a "Lesion" button to the GUI that simulates neural damage by:
   - Selecting a random rectangular region within the cmac_memory array
   - Replacing all values in that region with NaN (Not a Number) values
   - This should allow users to study how the CMAC performs with damaged/missing memory regions

3. **Add Nbuckets_per_hash parameter control**: Create an editable text box (edit box) in the GUI for the `Nbuckets_per_hash` parameter, similar to how the learning rate parameter is currently implemented. This should allow users to:
   - View the current value of buckets per hash function
   - Modify this value during runtime
   - See how changing the number of buckets affects CMAC performance and memory usage
